<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pydantic AI Chatbot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            padding-bottom: 180px; /* Space for floating pills */
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .chat-container {
            padding: 30px;
            min-height: 400px;
        }

        .question-pills {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
            min-height: 48px;
            align-items: center;
            margin-bottom: 20px;
        }

        .pill {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            background: #667eea;
            border: 1px solid #5a6fd8;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .pill:hover {
            background: #5a6fd8;
            border-color: #4c63d2;
            transform: translateY(-1px);
        }

        .pill:active {
            transform: translateY(0);
        }

        .pill:disabled {
            background: #6c757d;
            border-color: #6c757d;
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .response-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .response-text {
            font-size: 16px;
            line-height: 1.6;
            color: #2c3e50;
            white-space: pre-wrap;
        }

        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            animation: typing 1.4s infinite ease-in-out;
            margin-left: 4px;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #667eea;
            font-style: italic;
        }

        .error {
            background: #fee;
            border-left-color: #e74c3c;
            color: #c0392b;
        }

        .input-section {
            background: #f8f9fa;
            padding: 20px;
            border-top: 1px solid #e9ecef;
            margin-top: 20px;
            border-radius: 15px;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .text-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .text-input:focus {
            border-color: #667eea;
        }

        .send-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }

        .send-btn:hover {
            background: #5a6fd8;
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 14px;
            border-top: 1px solid #e9ecef;
        }

        .demo-badge {
            background: #ffd700;
            color: #000;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .chat-container {
                padding: 20px;
            }
            
            .question-pills {
                padding: 15px;
            }
            
            .pill {
                padding: 6px 12px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI Property Chatbot</h1>
            <p>Compare Pydantic AI vs LangGraph vs Google ADK implementations</p>
        </div>

        <div class="chat-container">
            <!-- AI Framework Toggle -->
            <div class="input-section" style="margin-bottom: 10px;">
                <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 15px;">
                    <label style="color: #495057; font-weight: 600;">AI Framework:</label>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="pill" id="pydanticToggle" style="background: #667eea; border-color: #5a6fd8;">Pydantic AI</button>
                        <button class="pill" id="langgraphToggle" style="background: #6c757d; border-color: #6c757d;">LangGraph</button>
                        <button class="pill" id="googleAdkToggle" style="background: #6c757d; border-color: #6c757d;">Google ADK</button>
                    </div>
                    <div id="frameworkStatus" style="font-size: 12px; color: #667eea; font-weight: 500;">Using Pydantic AI</div>
                </div>
                <div class="input-group" style="align-items: center;">
                    <label for="propertyIdInput" style="min-width: 110px; color: #495057;">Property ID</label>
                    <input 
                        type="text" 
                        class="text-input" 
                        id="propertyIdInput" 
                        placeholder="Enter property id"
                        value="4661571"
                        style="max-width: 260px;"
                    >
                </div>
                <div style="margin-top: 8px; font-size: 13px; color: #6c757d;">
                    Using GraphQL data. Default demo id is 4661571.
                </div>
            </div>
            <div id="responseContainer" style="display: none;">
                <!-- Responses will appear here -->
            </div>

            <div class="question-pills" id="questionPills">
                <!-- Pills will be loaded dynamically -->
            </div>

            <div class="input-section">
                <div class="input-group">
                    <input 
                        type="text" 
                        class="text-input" 
                        id="messageInput" 
                        placeholder="Ask about property value or growth, e.g., 'What's my house worth?'"
                    >
                    <button class="send-btn" id="sendBtn">Send</button>
                </div>
                <div style="margin-top: 10px; font-size: 14px; color: #6c757d;" id="frameworkDescription">
                    🤖 <strong>Pydantic AI:</strong> Tool-based agents with structured outputs. Type naturally or use buttons!
                </div>
            </div>
        </div>

    </div>

    <script>
        let currentFramework = 'pydantic'; // 'pydantic', 'langgraph', or 'google-adk'
        const API_BASES = {
            pydantic: 'http://localhost:8000/api',
            langgraph: 'http://localhost:8001/api/langgraph',
            'google-adk': 'http://localhost:8002/api/google-adk'
        };
        let isStreaming = false;
        
        function getCurrentApiBase() {
            return API_BASES[currentFramework];
        }

        // Framework switching functions
        function switchFramework(framework) {
            if (isStreaming) return; // Don't switch during streaming
            
            currentFramework = framework;
            
            // Update toggle buttons
            const pydanticBtn = document.getElementById('pydanticToggle');
            const langgraphBtn = document.getElementById('langgraphToggle');
            const googleAdkBtn = document.getElementById('googleAdkToggle');
            const status = document.getElementById('frameworkStatus');
            const description = document.getElementById('frameworkDescription');
            
            // Reset all buttons to inactive state
            [pydanticBtn, langgraphBtn, googleAdkBtn].forEach(btn => {
                btn.style.background = '#6c757d';
                btn.style.borderColor = '#6c757d';
            });
            
            // Activate selected framework
            if (framework === 'pydantic') {
                pydanticBtn.style.background = '#667eea';
                pydanticBtn.style.borderColor = '#5a6fd8';
                status.textContent = 'Using Pydantic AI';
                status.style.color = '#667eea';
                description.innerHTML = '🤖 <strong>Pydantic AI:</strong> Tool-based agents with structured outputs. Type naturally or use buttons!';
            } else if (framework === 'langgraph') {
                langgraphBtn.style.background = '#667eea';
                langgraphBtn.style.borderColor = '#5a6fd8';
                status.textContent = 'Using LangGraph';
                status.style.color = '#667eea';
                description.innerHTML = '🔄 <strong>LangGraph:</strong> State-based workflows with conditional routing. Type naturally or use buttons!';
            } else if (framework === 'google-adk') {
                googleAdkBtn.style.background = '#667eea';
                googleAdkBtn.style.borderColor = '#5a6fd8';
                status.textContent = 'Using Google ADK';
                status.style.color = '#667eea';
                description.innerHTML = '🤖 <strong>Google ADK:</strong> Multi-agent coordination with hierarchical delegation. Type naturally or use buttons!';
            }
            
            // Reload questions for the new framework
            loadQuestions();
        }
        
        // Load predefined questions on page load
        async function loadQuestions() {
            try {
                const response = await fetch(`${getCurrentApiBase()}/questions`);
                const data = await response.json();
                
                const pillsContainer = document.getElementById('questionPills');
                pillsContainer.innerHTML = '';
                
                data.questions.forEach(question => {
                    const pill = document.createElement('button');
                    pill.className = 'pill';
                    pill.textContent = question.display_text;
                    pill.title = question.description;
                    pill.onclick = () => askQuestion(question.id, question.display_text);
                    pillsContainer.appendChild(pill);
                });
            } catch (error) {
                console.error('Failed to load questions:', error);
                showError('Failed to load questions. Please check if the backend is running.');
            }
        }

        // Ask a predefined question
        async function askQuestion(questionType, questionText) {
            if (isStreaming) return;
            
            isStreaming = true;
            disablePills(true);
            
            // Show user question
            showUserMessage(questionText);
            
            // Show loading
            showLoading();
            
            try {
                // Use streaming endpoint for better demo effect
                await streamResponse(questionType);
            } catch (error) {
                console.error('Error:', error);
                showError('Sorry, something went wrong. Please try again.');
            } finally {
                isStreaming = false;
                disablePills(false);
            }
        }

        // Stream response from backend
        async function streamResponse(questionType) {
            const propertyId = (document.getElementById('propertyIdInput')?.value || '').trim();
            const apiBase = getCurrentApiBase();
            const url = propertyId
                ? `${apiBase}/chat/stream/${questionType}?property_id=${encodeURIComponent(propertyId)}`
                : `${apiBase}/chat/stream/${questionType}`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            
            hideLoading();
            const responseDiv = showBotResponse('');
            let fullResponse = '';

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                if (data.error) {
                                    showError(data.content);
                                    return;
                                }
                                
                                fullResponse += data.content;
                                responseDiv.textContent = fullResponse;
                                
                                if (data.is_complete) {
                                    // Add typing indicator removal delay
                                    setTimeout(() => {
                                        const indicator = responseDiv.querySelector('.typing-indicator');
                                        if (indicator) indicator.remove();
                                    }, 500);
                                } else {
                                    // Add typing indicator if not present
                                    if (!responseDiv.querySelector('.typing-indicator')) {
                                        const indicator = document.createElement('span');
                                        indicator.className = 'typing-indicator';
                                        responseDiv.appendChild(indicator);
                                    }
                                }
                                
                                // Scroll to bottom
                                responseDiv.scrollIntoView({ behavior: 'smooth' });
                                
                            } catch (e) {
                                console.error('Failed to parse SSE data:', e);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }
        }

        // UI Helper Functions
        function showUserMessage(message) {
            const responseContainer = document.getElementById('responseContainer');
            responseContainer.style.display = 'block';
            
            const userDiv = document.createElement('div');
            userDiv.className = 'response-container';
            userDiv.style.background = '#e3f2fd';
            userDiv.style.borderLeftColor = '#2196f3';
            userDiv.innerHTML = `<div class="response-text"><strong>You:</strong> ${message}</div>`;
            
            responseContainer.appendChild(userDiv);
        }

        function showBotResponse(text) {
            const responseContainer = document.getElementById('responseContainer');
            responseContainer.style.display = 'block';
            
            const botDiv = document.createElement('div');
            botDiv.className = 'response-container';
            botDiv.innerHTML = `<div class="response-text"></div>`;
            
            const textDiv = botDiv.querySelector('.response-text');
            textDiv.textContent = text;
            
            responseContainer.appendChild(botDiv);
            return textDiv;
        }

        function showLoading() {
            const responseContainer = document.getElementById('responseContainer');
            responseContainer.style.display = 'block';
            
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading';
            loadingDiv.id = 'loadingIndicator';
            loadingDiv.innerHTML = '🤖 Analyzing property data<span class="typing-indicator"></span><span class="typing-indicator"></span><span class="typing-indicator"></span>';
            
            responseContainer.appendChild(loadingDiv);
        }

        function hideLoading() {
            const loading = document.getElementById('loadingIndicator');
            if (loading) loading.remove();
        }

        function showError(message) {
            hideLoading();
            const responseContainer = document.getElementById('responseContainer');
            responseContainer.style.display = 'block';
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'response-container error';
            errorDiv.innerHTML = `<div class="response-text"><strong>Error:</strong> ${message}</div>`;
            
            responseContainer.appendChild(errorDiv);
        }

        function disablePills(disabled) {
            document.querySelectorAll('.pill').forEach(pill => {
                pill.disabled = disabled;
            });
        }

        // Handle free-form text input with intelligent classification
        async function sendFreeformMessage() {
            if (isStreaming) return;
            
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) {
                showError('Please enter a question about property valuation or growth.');
                return;
            }
            
            isStreaming = true;
            disablePills(true);
            messageInput.disabled = true;
            document.getElementById('sendBtn').disabled = true;
            
            // Show user question
            showUserMessage(message);
            
            // Show loading with classification message
            showLoadingWithClassification();
            
            try {
                // Use free-form streaming endpoint
                await streamFreeformResponse(message);
                
                // Clear input after successful send
                messageInput.value = '';
            } catch (error) {
                console.error('Error:', error);
                showError('Sorry, something went wrong. Please try again.');
            } finally {
                isStreaming = false;
                disablePills(false);
                messageInput.disabled = false;
                document.getElementById('sendBtn').disabled = false;
                messageInput.focus();
            }
        }
        
        // Stream response for free-form text
        async function streamFreeformResponse(message) {
            const propertyId = (document.getElementById('propertyIdInput')?.value || '').trim() || '4661571';
            const apiBase = getCurrentApiBase();
            
            const response = await fetch(`${apiBase}/chat/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    property_id: propertyId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            
            hideLoading();
            const responseDiv = showBotResponse('');
            let fullResponse = '';

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                if (data.error) {
                                    showError(data.content);
                                    return;
                                }
                                
                                fullResponse += data.content;
                                responseDiv.textContent = fullResponse;
                                
                                if (data.is_complete) {
                                    // Add typing indicator removal delay
                                    setTimeout(() => {
                                        const indicator = responseDiv.querySelector('.typing-indicator');
                                        if (indicator) indicator.remove();
                                    }, 500);
                                } else {
                                    // Add typing indicator if not present
                                    if (!responseDiv.querySelector('.typing-indicator')) {
                                        const indicator = document.createElement('span');
                                        indicator.className = 'typing-indicator';
                                        responseDiv.appendChild(indicator);
                                    }
                                }
                                
                                // Scroll to bottom
                                responseDiv.scrollIntoView({ behavior: 'smooth' });
                                
                            } catch (e) {
                                console.error('Failed to parse SSE data:', e);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }
        }
        
        function showLoadingWithClassification() {
            const responseContainer = document.getElementById('responseContainer');
            responseContainer.style.display = 'block';
            
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading';
            loadingDiv.id = 'loadingIndicator';
            const frameworkNames = {
                'pydantic': 'Pydantic AI',
                'langgraph': 'LangGraph', 
                'google-adk': 'Google ADK Multi-Agent System'
            };
            const frameworkName = frameworkNames[currentFramework] || 'Unknown Framework';
            loadingDiv.innerHTML = `🤖 ${frameworkName} classifying your question and analyzing property data<span class="typing-indicator"></span><span class="typing-indicator"></span><span class="typing-indicator"></span>`;
            
            responseContainer.appendChild(loadingDiv);
        }
        
        // Event listeners for text input
        document.getElementById('sendBtn').addEventListener('click', sendFreeformMessage);
        
        document.getElementById('messageInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendFreeformMessage();
            }
        });

        // Event listeners for framework switching
        document.getElementById('pydanticToggle').addEventListener('click', () => switchFramework('pydantic'));
        document.getElementById('langgraphToggle').addEventListener('click', () => switchFramework('langgraph'));
        document.getElementById('googleAdkToggle').addEventListener('click', () => switchFramework('google-adk'));
        
        // Initialize app
        loadQuestions();
    </script>
</body>
</html>
