# 🏠 AI Property Chatbot Demo (Pydantic AI vs LangGraph vs Google ADK)

A comparison demo showcasing **three different AI frameworks** for property analysis:
- **Pydantic AI**: Tool-based agents with structured outputs
- **LangGraph**: State-based workflows with conditional routing
- **Google ADK**: Multi-agent coordination with hierarchical delegation

## Run

```bash
./start_triple_demo.sh
```

All three frameworks use the same GraphQL data source, allowing direct comparison of AI approaches.

## Environment Set Up

```
# AI Model Access (Required for Pydantic AI, optional fallback for others)
OPENAI_API_KEY="sk-vQA7t1Anxm9mHZ0HIBt1JA"
OPENAI_BASE_URL="https://api.omnia.reainternal.net/"

# Property Data Source (Required for all frameworks)
GRAPHQL_ENDPOINT=https://pexa.property-services.resi-property.realestate.com.au/graphql

# Google ADK Configuration (Optional - enables full ADK features)
GOOGLE_CLOUD_PROJECT="your-project-id"
GOOGLE_CLOUD_REGION="us-central1"

# Observability (Optional - enables Langfuse tracing)
LANGFUSE_PUBLIC_KEY="pk-lf-21235df4-acbc-4d8d-af1e-e116365c37e8"
LANGFUSE_SECRET_KEY="******************************************"
LANGFUSE_HOST="https://langfuse-web.omnia.reainternal.net"
```

## Framework Comparison

| Feature | Pydantic AI | LangGraph | Google ADK |
|---------|-------------|-----------|------------|
| **Architecture** | Tool-based agents | State workflows | Multi-agent coordination |
| **Approach** | Direct tool calls | Graph-based routing | Hierarchical delegation |
| **Specialization** | Structured outputs | Conditional logic | Agent specialization |
| **Coordination** | Single agent | State management | Agent orchestration |
| **Scalability** | Moderate | High | Very High |
| **Complexity** | Low | Medium | High |

## Server Endpoints

- **Pydantic AI**: http://localhost:8000
- **LangGraph**: http://localhost:8001  
- **Google ADK**: http://localhost:8002
- **Frontend**: http://localhost:8000 (served by Pydantic AI)