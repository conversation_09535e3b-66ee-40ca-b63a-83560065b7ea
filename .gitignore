# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv/
venv/
ENV/
env/
env.bak/
venv.bak/

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
.pytest_cache/
.tox/
.nox/
coverage.xml
.coverage
.coverage.*
htmlcov/
.hypothesis/

# Logs
*.log
logs/

# IDE/editor
.vscode/
.idea/
*.iml

# OS files
.DS_Store
Thumbs.db

# Local env/config
.env
.env.*

# Cache
*.cache

# Frontend (if used later)
node_modules/
*.tsbuildinfo

