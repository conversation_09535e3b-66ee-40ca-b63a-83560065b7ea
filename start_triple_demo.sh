#!/bin/bash

# Start Pydantic AI, LangGraph, and Google ADK servers for comparison demo
# This script runs all three backends simultaneously on different ports

set -e

echo "🚀 Starting Triple AI Framework Property Demo..."
echo "   - Pydantic AI: http://localhost:8000"
echo "   - LangGraph: http://localhost:8001"
echo "   - Google ADK: http://localhost:8002"
echo "   - Frontend: http://localhost:8000 (served by Pydantic AI server)"
echo ""

# Function to cleanup background processes
cleanup() {
    echo "🛑 Shutting down servers..."
    if [[ ! -z "$PYDANTIC_PID" ]]; then
        kill $PYDANTIC_PID 2>/dev/null || true
    fi
    if [[ ! -z "$LANGGRAPH_PID" ]]; then
        kill $LANGGRAPH_PID 2>/dev/null || true
    fi
    if [[ ! -z "$GOOGLE_ADK_PID" ]]; then
        kill $GOOGLE_ADK_PID 2>/dev/null || true
    fi
    exit 0
}

# Set up cleanup trap
trap cleanup SIGINT SIGTERM

# Check if virtual environment exists, create if not
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Load environment variables from .env file
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️  Warning: .env file not found"
fi

# Show Langfuse tracing status
if [[ -n "$LANGFUSE_PUBLIC_KEY" && -n "$LANGFUSE_SECRET_KEY" ]]; then
    echo "🔭 Langfuse tracing enabled → ${LANGFUSE_HOST:-https://cloud.langfuse.com}"
else
    echo "🔭 Langfuse tracing disabled (missing keys)"
fi

# Install/update dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Start Pydantic AI server in background
echo "🤖 Starting Pydantic AI server (port 8000)..."
python -m backend.pydantic_ai.main &
PYDANTIC_PID=$!

# Wait a moment for first server to start
sleep 2

# Start LangGraph server in background
echo "🔄 Starting LangGraph server (port 8001)..."
python -m backend.langgraph.main &
LANGGRAPH_PID=$!

# Wait a moment for second server to start
sleep 2

# Start Google ADK server in background
echo "🤖 Starting Google ADK server (port 8002)..."
python -m backend.google_adk.main &
GOOGLE_ADK_PID=$!

# Wait for all servers to start
echo "⏳ Waiting for all servers to start..."
sleep 3

# Check if servers are running
if curl -s http://localhost:8000/api > /dev/null; then
    echo "✅ Pydantic AI server is running on port 8000"
else
    echo "❌ Failed to start Pydantic AI server"
    cleanup
fi

if curl -s http://localhost:8001/api/langgraph > /dev/null; then
    echo "✅ LangGraph server is running on port 8001"
else
    echo "❌ Failed to start LangGraph server"
    cleanup
fi

if curl -s http://localhost:8002/api/google-adk > /dev/null; then
    echo "✅ Google ADK server is running on port 8002"
else
    echo "❌ Failed to start Google ADK server"
    cleanup
fi

echo ""
echo "🎉 Triple Framework Demo is ready!"
echo "   📖 Open http://localhost:8000 in your browser"
echo "   🔄 Use the framework toggle to compare all three AI approaches"
echo "   🏠 Try asking: 'What's my property worth?' or 'How much has it grown?'"
echo ""
echo "💡 All frameworks use the same GraphQL data source but different AI approaches:"
echo "   • Pydantic AI: Tool-based agents with structured outputs"
echo "   • LangGraph: State-based workflows with conditional routing"
echo "   • Google ADK: Multi-agent coordination with hierarchical delegation"
echo ""

# Open browser if available
if command -v open &> /dev/null; then
    echo "🌐 Opening browser..."
    sleep 1
    open http://localhost:8000
elif command -v xdg-open &> /dev/null; then
    echo "🌐 Opening browser..."
    sleep 1
    xdg-open http://localhost:8000
fi

echo "Press Ctrl+C to stop all servers..."

# Wait for user to terminate
while true; do
    sleep 1
done