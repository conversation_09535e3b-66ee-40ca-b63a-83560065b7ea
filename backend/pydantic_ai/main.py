"""
FastAPI Backend for Pydantic AI Property Chatbot Demo
Showcases streaming responses and clean API design.
"""

import asyncio
import j<PERSON>
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

from backend.pydantic_ai.property_agent import (
    classify_user_question,
    format_response_for_streaming,
    format_unsupported_response,
    run_property_analysis_agent,
)
from backend.shared.models import (
    PREDEFINED_QUESTIONS,
    ErrorResponse,
    QuestionType,
    StreamChunk,
)

# (No direct use of pill models in this module)
from backend.shared.tracing import observe, update_current_observation


class ChatMessage(BaseModel):
    message: str
    property_id: str


# Initialize FastAPI app
app = FastAPI(
    title="Pydantic AI Property Chatbot Demo",
    description="A demo showcasing Pydantic AI framework capabilities for property analysis",
    version="1.0.0",
)

# CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/api")
async def root():
    """Health check endpoint"""
    return {"message": "Pydantic AI Property Chatbot Demo API", "status": "running"}


@app.get("/api/questions")
async def get_predefined_questions():
    """Get all predefined questions for the frontend pills"""
    return {"questions": PREDEFINED_QUESTIONS}


# NEW: Free-form text chat with intelligent question classification
@app.post("/api/chat/stream")
@observe(name="API: /api/chat/stream (pydantic_ai)")
async def chat_stream_freeform(chat_message: ChatMessage):
    """
    Streaming endpoint for free-form text input.
    Uses Pydantic AI to classify the question and route appropriately.
    Showcases intelligent question processing!
    """

    update_current_observation(
        input={
            "message": chat_message.message,
            "property_id": chat_message.property_id,
        }
    )

    async def generate_classified_stream() -> AsyncGenerator[str, None]:
        try:
            # Step 1: Use LLM to classify the user's question
            classification = await classify_user_question(chat_message.message)

            # Step 2: Handle based on classification
            if classification.question_type == QuestionType.UNSUPPORTED:
                # Show unsupported message with suggestions
                formatted_response = format_unsupported_response(classification)
                final_question_type = QuestionType.UNSUPPORTED
            else:
                # Process as normal using classified question type
                result_data = await run_property_analysis_agent(
                    classification.question_type, chat_message.property_id
                )
                formatted_response = format_response_for_streaming(
                    result_data, classification.question_type
                )
                final_question_type = classification.question_type

            # Step 3: Stream the response word by word
            words = formatted_response.split()
            total_words = len(words)

            for i, word in enumerate(words):
                is_complete = i == total_words - 1

                chunk = StreamChunk(
                    content=word + " ",
                    is_complete=is_complete,
                    question_type=final_question_type if is_complete else None,
                    available_questions=PREDEFINED_QUESTIONS if is_complete else None,
                )

                yield f"data: {json.dumps(chunk.dict())}\n\n"

                # Simulate typing delay for demo effect
                await asyncio.sleep(0.03)  # 30ms between words

        except Exception as e:
            # Error handling with streaming
            error_chunk = StreamChunk(
                content=f"Error processing your question: {str(e)}",
                is_complete=True,
                available_questions=PREDEFINED_QUESTIONS,
            )
            yield f"data: {json.dumps(error_chunk.dict())}\n\n"

    # Note: For streaming responses, we can't easily capture the final output
    # since it's yielded in chunks. The @observe decorator will capture
    # the function call and basic metadata.

    return StreamingResponse(
        generate_classified_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8",
        },
    )


@app.post("/api/chat/stream/{question_type}")
@observe(name="API: /api/chat/stream/{question_type} (pydantic_ai)")
async def chat_stream_by_type(
    question_type: QuestionType,
    property_id: str,
):
    """
    Streaming endpoint for predefined question types (pills).
    This showcases Pydantic AI with fake streaming effect.
    """

    update_current_observation(
        input={
            "question_type": str(question_type.value),
            "property_id": property_id,
        }
    )

    async def generate_analysis_stream() -> AsyncGenerator[str, None]:
        try:
            # Use Pydantic AI agent (falls back to direct GraphQL if no API key)
            result_data = await run_property_analysis_agent(question_type, property_id)

            # Format the complete response
            formatted_response = format_response_for_streaming(
                result_data, question_type
            )

            # Simulate streaming by sending word by word
            words = formatted_response.split()
            total_words = len(words)

            for i, word in enumerate(words):
                is_complete = i == total_words - 1

                chunk = StreamChunk(
                    content=word + " ",
                    is_complete=is_complete,
                    question_type=question_type if is_complete else None,
                    available_questions=PREDEFINED_QUESTIONS if is_complete else None,
                )

                yield f"data: {json.dumps(chunk.dict())}\n\n"

                # Simulate typing delay for demo effect
                await asyncio.sleep(0.03)  # 30ms between words

        except Exception as e:
            # Error handling with streaming
            error_chunk = StreamChunk(
                content=f"Error analyzing property: {str(e)}",
                is_complete=True,
                available_questions=PREDEFINED_QUESTIONS,
            )
            yield f"data: {json.dumps(error_chunk.dict())}\n\n"

    return StreamingResponse(
        generate_analysis_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8",
        },
    )


@app.get("/api/health")
async def health_check():
    """Health check for deployment monitoring"""
    return {
        "status": "healthy",
        "features": {
            "streaming": True,
            "predefined_questions": True,
            "graphql": True,
        },
    }


# Error handlers
@app.exception_handler(ValueError)
async def value_error_handler(request, exc):
    from fastapi.responses import JSONResponse

    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            error="Invalid request",
            message=str(exc),
            available_questions=PREDEFINED_QUESTIONS,
        ).dict(),
    )


@app.exception_handler(404)
async def not_found_handler(request, exc):
    from fastapi.responses import JSONResponse

    return JSONResponse(
        status_code=404,
        content=ErrorResponse(
            error="Endpoint not found",
            message="The requested endpoint does not exist",
            available_questions=PREDEFINED_QUESTIONS,
        ).dict(),
    )


# Mount static files at the end so API routes take precedence
app.mount("/", StaticFiles(directory="frontend", html=True), name="static")


if __name__ == "__main__":
    import uvicorn

    # For development with reload enabled
    # Note: With reload=True, you may need to press Ctrl+C twice on shutdown
    # This is a known uvicorn limitation with the reloader process
    uvicorn.run(
        "backend.pydantic_ai.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # Re-enabled for development
        log_level="info",
        reload_excludes=[
            "*.pyc",
            "__pycache__",
            ".git",
            "*.log",
        ],  # Optimize reload performance
        access_log=False,  # Reduce noise
    )
