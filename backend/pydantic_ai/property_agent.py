"""
Pydantic AI agent with GraphQL tools for simple demo pills.

- PROPERTY_VALUE: use GraphQL to fetch value + range
- HISTORICAL_GROWTH: use GraphQL to fetch delta since last sale
"""

import os
from typing import Union

from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openai import OpenAIProvider

from backend.shared.graphql_client import PEXAGraphQLClient
from backend.shared.models import (
    HistoricalGrowthAnswer,
    PropertyValueAnswer,
    QuestionClassification,
    QuestionType,
)
from backend.shared.tracing import mask_value, observe, update_current_observation


def get_model():
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    if not api_key:
        return None
    provider = OpenAIProvider(api_key=api_key, base_url=base_url)
    return OpenAIChatModel("gpt-4o-mini", provider=provider)


class GraphDeps:
    def __init__(self, property_id: str):
        self.client = PEXAGraphQLClient()
        self.property_id = property_id


property_agent = Agent(
    model=get_model(),
    deps_type=GraphDeps,
    output_type=Union[PropertyValueAnswer, HistoricalGrowthAnswer],
    system_prompt=(
        "You are a concise property analysis agent. "
        "Always use the provided tools to fetch data. "
        "For property_value: call get_property_value_by_id. "
        "For historical_growth: call get_historical_growth_by_id. "
        "Return only the structured output."
    ),
)


@property_agent.tool
@observe(name="Tool: get_property_value_by_id")
async def get_property_value_by_id(ctx: RunContext[GraphDeps]) -> PropertyValueAnswer:
    update_current_observation(input={"property_id": mask_value(ctx.deps.property_id)})
    gql = await ctx.deps.client.get_property_by_id(ctx.deps.property_id)
    return ctx.deps.client.to_property_value_answer(gql)


@property_agent.tool
@observe(name="Tool: get_historical_growth_by_id")
async def get_historical_growth_by_id(
    ctx: RunContext[GraphDeps],
) -> HistoricalGrowthAnswer:
    update_current_observation(input={"property_id": mask_value(ctx.deps.property_id)})
    gql = await ctx.deps.client.get_property_by_id(ctx.deps.property_id)
    return ctx.deps.client.to_historical_growth_answer(gql)


def should_use_agent() -> bool:
    key = os.getenv("OPENAI_API_KEY") or ""
    return bool(key.strip())


@observe(name="PAI: run_property_analysis_agent")
async def run_property_analysis_agent(
    question_type: QuestionType, property_id: str
) -> Union[PropertyValueAnswer, HistoricalGrowthAnswer]:
    update_current_observation(
        input={
            "question_type": str(
                question_type.value
                if hasattr(question_type, "value")
                else question_type
            ),
            "property_id": mask_value(property_id),
        }
    )
    if not should_use_agent():
        # Fallback path if no API key: call tools directly via client
        client = PEXAGraphQLClient()
        gql = await client.get_property_by_id(property_id)
        if question_type == QuestionType.PROPERTY_VALUE:
            return client.to_property_value_answer(gql)
        elif question_type == QuestionType.HISTORICAL_GROWTH:
            return client.to_historical_growth_answer(gql)
        else:
            raise ValueError(f"Unsupported question type: {question_type}")

    if question_type == QuestionType.PROPERTY_VALUE:
        prompt = (
            "User asks for current property valuation. "
            "Call get_property_value_by_id with the given id and return the structured result."
        )
    elif question_type == QuestionType.HISTORICAL_GROWTH:
        prompt = (
            "User asks for historical growth since last sale. "
            "Call get_historical_growth_by_id with the given id and return the structured result."
        )
    else:
        raise ValueError(f"Unsupported question type: {question_type}")

    result = await property_agent.run(prompt, deps=GraphDeps(property_id))
    return result.output


def format_response_for_streaming(
    response_data: Union[PropertyValueAnswer, HistoricalGrowthAnswer],
    question_type: QuestionType,
) -> str:
    if question_type == QuestionType.PROPERTY_VALUE and isinstance(
        response_data, PropertyValueAnswer
    ):
        return (
            f"**Property Valuation**\n\n"
            f"🏠 Estimated Value: ${response_data.value:,}\n"
            f"📈 Range: ${response_data.range.min:,} - ${response_data.range.max:,}"
            f"{f'\n🧭 Confidence: {response_data.confidence}' if response_data.confidence else ''}"
            f"{f'\n📍 {response_data.full_address}' if response_data.full_address else ''}"
        )

    if question_type == QuestionType.HISTORICAL_GROWTH and isinstance(
        response_data, HistoricalGrowthAnswer
    ):
        delta_emoji = "📈" if response_data.delta_since_last_sale >= 0 else "📉"
        return (
            f"**Historical Growth (Since Last Sale)**\n\n"
            f"{delta_emoji} Delta Since Last Sold: ${response_data.delta_since_last_sale:,}\n"
            f"💵 Current Value: ${response_data.current_value:,}"
            f"{f'\n💰 Last Sold: ${response_data.last_sold_price:,} ({response_data.last_sold_year})' if response_data.last_sold_price else ''}"
            f"{f'\n📍 {response_data.full_address}' if response_data.full_address else ''}"
        )

    return "Unable to format response."


# Question Classification Agent
question_classifier = Agent(
    model=get_model(),
    output_type=QuestionClassification,
    system_prompt="""
    You are a question classification expert for a property analysis system.
    
    Analyze the user's question and determine if it matches one of these supported types:
    
    1. PROPERTY_VALUE - Questions about current property worth, valuation, market value, price
       Examples: "What's my property worth?", "How much is my house valued at?", "Current property price?"
    
    2. HISTORICAL_GROWTH - Questions about property value changes, growth, historical performance
       Examples: "How much has my property grown?", "Property value increase over time?", "Historical performance?"
    
    3. UNSUPPORTED - Any other questions we cannot answer
    
    Be generous in matching - if a question is reasonably close to our supported types, classify it accordingly.
    Only use UNSUPPORTED if the question is clearly about something else entirely.
    
    Provide a confidence score (0.0-1.0) and brief reasoning for your classification.
    If unsupported, suggest similar questions we can help with.
    """,
)


@observe(name="PAI: classify_user_question")
async def classify_user_question(user_question: str) -> QuestionClassification:
    """Use LLM to intelligently classify user's natural language question"""
    update_current_observation(input={"user_question": user_question})

    if not should_use_agent():
        # Fallback: simple keyword matching without LLM
        user_lower = user_question.lower()

        value_keywords = ["worth", "value", "price", "cost", "valuation", "market"]
        growth_keywords = [
            "growth",
            "grown",
            "increase",
            "change",
            "historical",
            "performance",
        ]

        value_score = sum(1 for keyword in value_keywords if keyword in user_lower)
        growth_score = sum(1 for keyword in growth_keywords if keyword in user_lower)

        if value_score > growth_score and value_score > 0:
            return QuestionClassification(
                question_type=QuestionType.PROPERTY_VALUE,
                confidence=min(0.8, value_score * 0.3),
                reasoning=f"Detected value-related keywords: {[k for k in value_keywords if k in user_lower]}",
            )
        elif growth_score > 0:
            return QuestionClassification(
                question_type=QuestionType.HISTORICAL_GROWTH,
                confidence=min(0.8, growth_score * 0.3),
                reasoning=f"Detected growth-related keywords: {[k for k in growth_keywords if k in user_lower]}",
            )
        else:
            return QuestionClassification(
                question_type=QuestionType.UNSUPPORTED,
                confidence=0.9,
                reasoning="No matching keywords found for supported question types",
                suggested_alternatives=[
                    "What's my property worth?",
                    "How much has my property grown in value?",
                ],
            )

    # Use LLM for intelligent classification
    result = await question_classifier.run(f"User question: '{user_question}'")
    return result.output


def format_unsupported_response(classification: QuestionClassification) -> str:
    """Format a helpful response for unsupported questions"""
    base_message = "I'm sorry, but I can only help with property valuation and historical growth questions."

    if classification.suggested_alternatives:
        suggestions = "\n\n💡 **Try asking:**\n" + "\n".join(
            f"• {suggestion}" for suggestion in classification.suggested_alternatives
        )
    else:
        suggestions = "\n\n💡 **Try asking:**\n• What's my property worth?\n• How much has my property grown in value?"

    return base_message + suggestions
