"""
Google ADK agent definitions for property analysis.
Demonstrates multi-agent architecture with specialized sub-agents.
"""

import os
from typing import Optional

from google.adk.agents import LlmAgent

from backend.shared.graphql_client import PEXAGraphQLClient
from backend.shared.models import QuestionType
from backend.shared.tracing import observe, update_current_observation


def get_model_name() -> str:
    """Get the model name for ADK agents"""
    return "gemini-2.0-flash-exp"  # Latest Gemini model


def should_use_agent() -> bool:
    """Check if we have required credentials for Google ADK"""
    return bool(
        os.getenv("GOOGLE_CLOUD_PROJECT") 
        and os.getenv("OPENAI_API_KEY")  # Fallback to OpenAI if no Google creds
    )


# Specialized agent for property value analysis
property_value_agent = LlmAgent(
    name="property_value_specialist",
    model=get_model_name(),
    instruction="""
    You are a specialized property valuation expert. 
    When given a property ID, you will:
    1. Fetch the current market valuation data
    2. Analyze the valuation range and confidence level
    3. Present the results in a clear, professional format
    
    Focus only on current market value, estimated price ranges, and valuation confidence.
    Be concise but informative in your responses.
    """,
    description="Specialized agent for current property market valuations",
)

# Specialized agent for historical growth analysis  
historical_growth_agent = LlmAgent(
    name="historical_growth_specialist", 
    model=get_model_name(),
    instruction="""
    You are a specialized property growth analyst.
    When given a property ID, you will:
    1. Fetch historical property data including last sale information
    2. Calculate growth/change since the last sale
    3. Analyze performance trends and present insights
    
    Focus on historical performance, growth calculations, and trend analysis.
    Present data with clear comparisons between past and present values.
    """,
    description="Specialized agent for property historical growth analysis",
)

# Question classification specialist
question_classifier_agent = LlmAgent(
    name="question_classifier",
    model=get_model_name(), 
    instruction="""
    You are a question classification expert for property analysis.
    
    Analyze user questions and classify them into these categories:
    
    1. PROPERTY_VALUE - Questions about current worth, valuation, market value, price
       Keywords: "worth", "value", "price", "cost", "valuation", "market", "estimate"
       
    2. HISTORICAL_GROWTH - Questions about growth, performance, changes over time
       Keywords: "growth", "grown", "increase", "change", "historical", "performance", "trend"
       
    3. UNSUPPORTED - Any other questions outside our scope
    
    Respond with ONLY the classification: PROPERTY_VALUE, HISTORICAL_GROWTH, or UNSUPPORTED
    Be generous in matching - if reasonably close to supported types, classify accordingly.
    """,
    description="Classifies user questions into supported property analysis categories",
)

# Main coordinator agent that orchestrates sub-agents
coordinator_agent = LlmAgent(
    name="property_analysis_coordinator",
    model=get_model_name(),
    instruction="""
    You are the main coordinator for a property analysis system.
    
    Your role is to:
    1. Understand user questions about properties
    2. Route requests to appropriate specialist agents
    3. Coordinate responses from multiple agents
    4. Present final results in a clear, comprehensive format
    
    You have access to specialized agents for:
    - Property valuation (current market value)
    - Historical growth analysis (performance over time)
    - Question classification
    
    Always delegate to the appropriate specialist and synthesize their responses.
    """,
    description="Main coordinator for property analysis multi-agent system",
    sub_agents=[property_value_agent, historical_growth_agent, question_classifier_agent],
)


@observe(name="ADK: get_specialized_agent")
def get_specialized_agent(question_type: QuestionType) -> LlmAgent:
    """Get the appropriate specialized agent for the question type"""
    update_current_observation(input={"question_type": str(question_type.value)})
    
    if question_type == QuestionType.PROPERTY_VALUE:
        return property_value_agent
    elif question_type == QuestionType.HISTORICAL_GROWTH:
        return historical_growth_agent
    else:
        # For unsupported questions, use the coordinator to provide guidance
        return coordinator_agent


def create_custom_tool_agent(question_type: QuestionType, property_id: str) -> LlmAgent:
    """
    Create a custom ADK agent with tools for property analysis.
    This demonstrates ADK's tool integration capabilities.
    """
    
    async def fetch_property_data():
        """Custom tool function to fetch property data"""
        client = PEXAGraphQLClient()
        return await client.get_property_by_id(property_id)
    
    # Create a specialized agent with custom tools
    tool_agent = LlmAgent(
        name=f"property_tool_agent_{question_type.value}",
        model=get_model_name(),
        instruction=f"""
        You are a property analysis agent with access to property data tools.
        Your task is to analyze property data for question type: {question_type.value}
        
        Use the available tools to fetch property data and provide comprehensive analysis.
        Format your response professionally with clear insights and data.
        """,
        description=f"Tool-enabled agent for {question_type.value} analysis",
    )
    
    return tool_agent


# Helper function to get the main entry point agent
def get_main_agent() -> LlmAgent:
    """Get the main coordinator agent for the ADK system"""
    return coordinator_agent


# Agent status and health check
def get_agent_status() -> dict:
    """Get status information about all ADK agents"""
    return {
        "coordinator": {
            "name": coordinator_agent.name,
            "model": coordinator_agent.model,
            "sub_agents": len(coordinator_agent.sub_agents or []),
        },
        "specialists": {
            "property_value": property_value_agent.name,
            "historical_growth": historical_growth_agent.name,  
            "question_classifier": question_classifier_agent.name,
        },
        "available": should_use_agent(),
    }