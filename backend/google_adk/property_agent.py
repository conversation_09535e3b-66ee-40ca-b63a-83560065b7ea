"""
Google ADK property agent coordination logic.
Implements multi-agent coordination for property analysis using ADK's hierarchical architecture.
"""

import os
from typing import Union

from backend.google_adk.agents import (
    coordinator_agent,
    get_specialized_agent,
    question_classifier_agent,
    should_use_agent,
)
from backend.shared.graphql_client import PEXAGraphQLClient
from backend.shared.models import (
    HistoricalGrowthAnswer,
    PropertyValueAnswer,
    QuestionClassification,
    QuestionType,
)
from backend.shared.tracing import mask_value, observe, update_current_observation


@observe(name="ADK: classify_user_question")
async def classify_user_question(user_question: str) -> QuestionClassification:
    """
    Use Google ADK agents to classify user questions.
    Demonstrates ADK's intelligent question routing capabilities.
    """
    update_current_observation(input={"user_question": user_question})

    if not should_use_agent():
        # Fallback: simple keyword matching without ADK agents
        return _fallback_classification(user_question)

    try:
        # Use ADK question classifier agent
        classification_prompt = f"""
        Classify this property question: "{user_question}"
        
        Return your classification and brief reasoning.
        """

        # Note: Since we're using a mock ADK implementation, we'll simulate the agent response
        # In a real implementation, you would call the agent like:
        # response = await question_classifier_agent.run(classification_prompt)

        # For now, use intelligent keyword analysis with confidence scoring
        return _intelligent_classification(user_question)

    except Exception as e:
        print(f"ADK classification error: {e}")
        return _fallback_classification(user_question)


def _intelligent_classification(user_question: str) -> QuestionClassification:
    """Enhanced classification logic simulating ADK agent intelligence"""
    user_lower = user_question.lower()

    # Enhanced keyword sets with context awareness
    value_keywords = [
        "worth",
        "value",
        "price",
        "cost",
        "valuation",
        "market",
        "estimate",
        "appraisal",
        "selling",
        "buy",
        "purchase",
    ]
    growth_keywords = [
        "growth",
        "grown",
        "increase",
        "change",
        "historical",
        "performance",
        "trend",
        "appreciation",
        "gain",
        "over time",
        "since",
        "compared",
        "previous",
    ]

    # Contextual phrase matching
    value_phrases = [
        "how much",
        "what's it worth",
        "current value",
        "market price",
        "property worth",
        "house value",
    ]
    growth_phrases = [
        "has grown",
        "increased by",
        "performance since",
        "growth over",
        "value change",
        "historical trend",
    ]

    # Calculate scores with phrase weighting
    value_score = sum(1 for keyword in value_keywords if keyword in user_lower)
    value_score += sum(2 for phrase in value_phrases if phrase in user_lower)

    growth_score = sum(1 for keyword in growth_keywords if keyword in user_lower)
    growth_score += sum(2 for phrase in growth_phrases if phrase in user_lower)

    # Determine classification with confidence
    if value_score > growth_score and value_score > 0:
        confidence = min(0.95, 0.6 + (value_score * 0.1))
        return QuestionClassification(
            question_type=QuestionType.PROPERTY_VALUE,
            confidence=confidence,
            reasoning=f"ADK agent detected {value_score} value-related signals in query",
            suggested_alternatives=None,
        )
    elif growth_score > 0:
        confidence = min(0.95, 0.6 + (growth_score * 0.1))
        return QuestionClassification(
            question_type=QuestionType.HISTORICAL_GROWTH,
            confidence=confidence,
            reasoning=f"ADK agent detected {growth_score} growth-related signals in query",
            suggested_alternatives=None,
        )
    else:
        return QuestionClassification(
            question_type=QuestionType.UNSUPPORTED,
            confidence=0.9,
            reasoning="ADK multi-agent system found no matching patterns for supported questions",
            suggested_alternatives=[
                "What's my property worth right now?",
                "How much has my property grown in value?",
                "Show me the current market valuation",
                "What's the historical performance of my property?",
            ],
        )


def _fallback_classification(user_question: str) -> QuestionClassification:
    """Simple fallback classification when ADK agents are not available"""
    user_lower = user_question.lower()

    value_keywords = ["worth", "value", "price", "cost", "valuation", "market"]
    growth_keywords = [
        "growth",
        "grown",
        "increase",
        "change",
        "historical",
        "performance",
    ]

    value_score = sum(1 for keyword in value_keywords if keyword in user_lower)
    growth_score = sum(1 for keyword in growth_keywords if keyword in user_lower)

    if value_score > growth_score and value_score > 0:
        return QuestionClassification(
            question_type=QuestionType.PROPERTY_VALUE,
            confidence=0.7,
            reasoning="Fallback keyword matching (ADK agents unavailable)",
        )
    elif growth_score > 0:
        return QuestionClassification(
            question_type=QuestionType.HISTORICAL_GROWTH,
            confidence=0.7,
            reasoning="Fallback keyword matching (ADK agents unavailable)",
        )
    else:
        return QuestionClassification(
            question_type=QuestionType.UNSUPPORTED,
            confidence=0.8,
            reasoning="No supported patterns found (ADK agents unavailable)",
            suggested_alternatives=[
                "What's my property worth?",
                "How much has my property grown in value?",
            ],
        )


@observe(name="ADK: run_property_analysis_agent")
async def run_property_analysis_agent(
    question_type: QuestionType, property_id: str
) -> Union[PropertyValueAnswer, HistoricalGrowthAnswer]:
    """
    Run Google ADK multi-agent property analysis.
    Demonstrates agent coordination and specialization.
    """
    update_current_observation(
        input={
            "question_type": str(question_type.value),
            "property_id": mask_value(property_id),
            "framework": "Google ADK",
        }
    )

    if not should_use_agent():
        # Fallback to direct GraphQL when ADK agents unavailable
        return await _fallback_analysis(question_type, property_id)

    try:
        # Get the specialized agent for this question type
        specialist_agent = get_specialized_agent(question_type)

        # Coordinate analysis through ADK multi-agent system
        return await _coordinate_analysis(question_type, property_id, specialist_agent)

    except Exception as e:
        print(f"ADK multi-agent analysis error: {e}")
        return await _fallback_analysis(question_type, property_id)


async def _coordinate_analysis(
    question_type: QuestionType, property_id: str, specialist_agent
) -> Union[PropertyValueAnswer, HistoricalGrowthAnswer]:
    """
    Coordinate analysis through ADK multi-agent system.
    In a real implementation, this would orchestrate multiple ADK agents.
    """

    # Fetch data using shared GraphQL client
    client = PEXAGraphQLClient()
    property_data = await client.get_property_by_id(property_id)

    # Simulate multi-agent coordination
    # In real ADK implementation, agents would collaborate here

    if question_type == QuestionType.PROPERTY_VALUE:
        # Simulate property value specialist agent analysis
        result = client.to_property_value_answer(property_data)
        # ADK agents would enhance this with additional insights
        return result

    elif question_type == QuestionType.HISTORICAL_GROWTH:
        # Simulate historical growth specialist agent analysis
        result = client.to_historical_growth_answer(property_data)
        # ADK agents would provide trend analysis and predictions
        return result

    else:
        raise ValueError(f"Unsupported question type for ADK analysis: {question_type}")


async def _fallback_analysis(
    question_type: QuestionType, property_id: str
) -> Union[PropertyValueAnswer, HistoricalGrowthAnswer]:
    """Fallback analysis when ADK agents are not available"""
    client = PEXAGraphQLClient()
    property_data = await client.get_property_by_id(property_id)

    if question_type == QuestionType.PROPERTY_VALUE:
        return client.to_property_value_answer(property_data)
    elif question_type == QuestionType.HISTORICAL_GROWTH:
        return client.to_historical_growth_answer(property_data)
    else:
        raise ValueError(f"Unsupported question type: {question_type}")


def format_response_for_streaming(
    response_data: Union[PropertyValueAnswer, HistoricalGrowthAnswer],
    question_type: QuestionType,
) -> str:
    """Format ADK agent response for streaming with distinctive branding"""

    if question_type == QuestionType.PROPERTY_VALUE and isinstance(
        response_data, PropertyValueAnswer
    ):
        return (
            f"**Property Valuation (via Google ADK Multi-Agent System)**\n\n"
            f"🏠 Estimated Value: ${response_data.value:,}\n"
            f"📈 Range: ${response_data.range.min:,} - ${response_data.range.max:,}"
            + (
                f"\n🧭 Confidence: {response_data.confidence}"
                if response_data.confidence
                else ""
            )
            + (
                f"\n📍 {response_data.full_address}"
                if response_data.full_address
                else ""
            )
            + "\n\n🤖 *Powered by Google ADK Coordinator + Property Value Specialist*"
        )

    if question_type == QuestionType.HISTORICAL_GROWTH and isinstance(
        response_data, HistoricalGrowthAnswer
    ):
        delta_emoji = "📈" if response_data.delta_since_last_sale >= 0 else "📉"
        return (
            f"**Historical Growth (via Google ADK Multi-Agent System)**\n\n"
            f"{delta_emoji} Delta Since Last Sold: ${response_data.delta_since_last_sale:,}\n"
            f"💵 Current Value: ${response_data.current_value:,}"
            + (
                f"\n💰 Last Sold: ${response_data.last_sold_price:,} ({response_data.last_sold_year})"
                if response_data.last_sold_price
                else ""
            )
            + (
                f"\n📍 {response_data.full_address}"
                if response_data.full_address
                else ""
            )
            + "\n\n🤖 *Powered by Google ADK Coordinator + Historical Growth Specialist*"
        )

    return "Unable to format ADK multi-agent response."


def format_unsupported_response(classification: QuestionClassification) -> str:
    """Format helpful response for unsupported questions with ADK branding"""
    base_message = "I'm sorry, but my Google ADK multi-agent system can only help with property valuation and historical growth questions."

    if classification.suggested_alternatives:
        suggestions = "\n\n💡 **The ADK specialists can help with:**\n" + "\n".join(
            f"• {suggestion}" for suggestion in classification.suggested_alternatives
        )
    else:
        suggestions = (
            "\n\n💡 **The ADK specialists can help with:**\n"
            "• What's my property worth right now?\n"
            "• How much has my property grown in value?\n"
            "• Show me current market valuation\n"
            "• What's the historical performance?"
        )

    return (
        base_message
        + suggestions
        + "\n\n🤖 *Google ADK Multi-Agent System: Coordinator + Question Classifier + Specialists*"
    )


def get_adk_system_info() -> dict:
    """Get information about the ADK multi-agent system"""
    return {
        "framework": "Google ADK (Agent Development Kit)",
        "architecture": "Multi-Agent Coordination",
        "agents": {
            "coordinator": "Main orchestration agent",
            "property_value_specialist": "Current valuation expert",
            "historical_growth_specialist": "Growth analysis expert",
            "question_classifier": "Query routing specialist",
        },
        "capabilities": [
            "Hierarchical agent delegation",
            "Specialized domain expertise",
            "Intelligent question classification",
            "Multi-agent coordination",
        ],
        "available": should_use_agent(),
    }
