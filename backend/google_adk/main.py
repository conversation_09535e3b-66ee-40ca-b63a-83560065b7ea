"""
FastAPI Backend for Google ADK Property Chatbot Demo
Demonstrates multi-agent coordination and hierarchical agent architecture.
"""

import async<PERSON>
import json
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from backend.google_adk.property_agent import (
    classify_user_question,
    format_response_for_streaming,
    format_unsupported_response,
    get_adk_system_info,
    run_property_analysis_agent,
)
from backend.shared.models import (
    PREDEFINED_QUESTIONS,
    ErrorResponse,
    QuestionType,
    StreamChunk,
)
from backend.shared.tracing import observe, update_current_observation


class ChatMessage(BaseModel):
    message: str
    property_id: str


# Initialize FastAPI app
app = FastAPI(
    title="Google ADK Property Chatbot Demo",
    description="A demo showcasing Google ADK multi-agent capabilities for property analysis",
    version="1.0.0",
)

# CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/api/google-adk")
async def root():
    """Health check endpoint for Google ADK backend"""
    return {
        "message": "Google ADK Property Chatbot Demo API", 
        "status": "running",
        "framework": "Google Agent Development Kit"
    }


@app.get("/api/google-adk/questions")
async def get_predefined_questions():
    """Get all predefined questions for the frontend pills"""
    return {"questions": PREDEFINED_QUESTIONS}


@app.get("/api/google-adk/system")
async def get_system_info():
    """Get information about the ADK multi-agent system"""
    return get_adk_system_info()


# Free-form text chat with Google ADK multi-agent classification
@app.post("/api/google-adk/chat/stream")
@observe(name="API: /api/google-adk/chat/stream")
async def chat_stream_freeform(chat_message: ChatMessage):
    """
    Streaming endpoint for free-form text input using Google ADK multi-agent system.
    Uses hierarchical agent coordination to classify questions and route processing.
    """

    update_current_observation(
        input={
            "message": chat_message.message,
            "property_id": chat_message.property_id,
            "framework": "Google ADK",
        }
    )

    async def generate_classified_stream() -> AsyncGenerator[str, None]:
        try:
            # Step 1: Use ADK multi-agent system to classify the user's question
            classification = await classify_user_question(chat_message.message)

            # Step 2: Handle based on classification using appropriate agents
            if classification.question_type == QuestionType.UNSUPPORTED:
                # Show unsupported message with ADK-specific suggestions
                formatted_response = format_unsupported_response(classification)
                final_question_type = QuestionType.UNSUPPORTED
            else:
                # Process using ADK multi-agent coordination
                result_data = await run_property_analysis_agent(
                    classification.question_type, chat_message.property_id
                )
                formatted_response = format_response_for_streaming(
                    result_data, classification.question_type
                )
                final_question_type = classification.question_type

            # Step 3: Stream the response word by word
            words = formatted_response.split()
            total_words = len(words)

            for i, word in enumerate(words):
                is_complete = i == total_words - 1

                chunk = StreamChunk(
                    content=word + " ",
                    is_complete=is_complete,
                    question_type=final_question_type if is_complete else None,
                    available_questions=PREDEFINED_QUESTIONS if is_complete else None,
                )

                yield f"data: {json.dumps(chunk.dict())}\n\n"

                # Simulate typing delay for demo effect
                await asyncio.sleep(0.03)  # 30ms between words

        except Exception as e:
            # Error handling with streaming
            error_chunk = StreamChunk(
                content=f"Error processing your question with Google ADK multi-agent system: {str(e)}",
                is_complete=True,
                available_questions=PREDEFINED_QUESTIONS,
            )
            yield f"data: {json.dumps(error_chunk.dict())}\n\n"

    return StreamingResponse(
        generate_classified_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8",
        },
    )


@app.post("/api/google-adk/chat/stream/{question_type}")
@observe(name="API: /api/google-adk/chat/stream/{question_type}")
async def chat_stream_by_type(
    question_type: QuestionType,
    property_id: str,
):
    """
    Streaming endpoint for predefined question types (pills) using Google ADK.
    This showcases ADK multi-agent coordination with streaming effect.
    """

    update_current_observation(
        input={
            "question_type": str(question_type.value),
            "property_id": property_id,
            "framework": "Google ADK",
        }
    )

    async def generate_analysis_stream() -> AsyncGenerator[str, None]:
        try:
            # Use Google ADK multi-agent system for property analysis
            result_data = await run_property_analysis_agent(question_type, property_id)

            # Format the complete response with ADK branding
            formatted_response = format_response_for_streaming(
                result_data, question_type
            )

            # Simulate streaming by sending word by word
            words = formatted_response.split()
            total_words = len(words)

            for i, word in enumerate(words):
                is_complete = i == total_words - 1

                chunk = StreamChunk(
                    content=word + " ",
                    is_complete=is_complete,
                    question_type=question_type if is_complete else None,
                    available_questions=PREDEFINED_QUESTIONS if is_complete else None,
                )

                yield f"data: {json.dumps(chunk.dict())}\n\n"

                # Simulate typing delay for demo effect
                await asyncio.sleep(0.03)  # 30ms between words

        except Exception as e:
            # Error handling with streaming
            error_chunk = StreamChunk(
                content=f"Error analyzing property with Google ADK: {str(e)}",
                is_complete=True,
                available_questions=PREDEFINED_QUESTIONS,
            )
            yield f"data: {json.dumps(error_chunk.dict())}\n\n"

    return StreamingResponse(
        generate_analysis_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8",
        },
    )


@app.get("/api/google-adk/health")
async def health_check():
    """Health check for Google ADK deployment monitoring"""
    return {
        "status": "healthy",
        "framework": "Google ADK",
        "version": "1.0.0",
        "features": {
            "streaming": True,
            "multi_agent_coordination": True,
            "hierarchical_agents": True,
            "predefined_questions": True,
            "graphql": True,
        },
        "agents": {
            "coordinator": "Main orchestration agent",
            "specialists": ["property_value", "historical_growth", "question_classifier"]
        },
    }


@app.get("/api/google-adk/agents")
async def get_agents_status():
    """Get detailed status of all ADK agents"""
    from backend.google_adk.agents import get_agent_status
    return get_agent_status()


# Error handlers
@app.exception_handler(ValueError)
async def value_error_handler(request, exc):
    from fastapi.responses import JSONResponse

    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            error="Invalid request (Google ADK)",
            message=str(exc),
            available_questions=PREDEFINED_QUESTIONS,
        ).dict(),
    )


@app.exception_handler(404)
async def not_found_handler(request, exc):
    from fastapi.responses import JSONResponse

    return JSONResponse(
        status_code=404,
        content=ErrorResponse(
            error="Endpoint not found (Google ADK)",
            message="The requested ADK endpoint does not exist",
            available_questions=PREDEFINED_QUESTIONS,
        ).dict(),
    )


if __name__ == "__main__":
    import uvicorn

    # For development with reload enabled
    uvicorn.run(
        "backend.google_adk.main:app",
        host="0.0.0.0",
        port=8002,  # Different port from Pydantic AI (8000) and LangGraph (8001)
        reload=True,
        log_level="info",
        reload_excludes=[
            "*.pyc",
            "__pycache__",
            ".git",
            "*.log",
        ],
        access_log=False,
    )