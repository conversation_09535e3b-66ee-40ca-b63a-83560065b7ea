from enum import Enum
from typing import List, Optional, Union

from pydantic import BaseModel, Field


class QuestionType(str, Enum):
    PROPERTY_VALUE = "property_value"
    HISTORICAL_GROWTH = "historical_growth"
    UNSUPPORTED = "unsupported"


class PredefinedQuestion(BaseModel):
    id: str
    display_text: str
    description: str


class StreamChunk(BaseModel):
    content: str
    is_complete: bool
    question_type: Optional[QuestionType] = None
    available_questions: Optional[List[PredefinedQuestion]] = None


class ErrorResponse(BaseModel):
    error: str
    message: str
    available_questions: List[PredefinedQuestion]


# Predefined questions configuration
PREDEFINED_QUESTIONS = [
    PredefinedQuestion(
        id="property_value",
        display_text="What's my property worth?",
        description="Get current market valuation of your property",
    ),
    PredefinedQuestion(
        id="historical_growth",
        display_text="How much has my property grown in value?",
        description="Calculate historical performance since purchase",
    ),
]


# -----------------------------
# PEXA GraphQL data structures
# -----------------------------


class PEXAValuationRange(BaseModel):
    min: int
    max: int


class PEXADeltaSinceLastSale(BaseModel):
    value: int


class PEXALastSoldData(BaseModel):
    price: Optional[int] = None
    year: Optional[str] = None


class PEXASaleValuation(BaseModel):
    value: int
    range: PEXAValuationRange
    confidence: Optional[str] = None  # e.g. "high" | "medium" | "low"
    deltaSinceLastSale: Optional[PEXADeltaSinceLastSale] = None
    lastSoldData: Optional[PEXALastSoldData] = None


class PEXAValuations(BaseModel):
    sale: PEXASaleValuation


class PEXAProperty(BaseModel):
    id: str
    suburb: Optional[str] = None
    postcode: Optional[str] = None
    fullAddress: Optional[str] = None
    valuations: PEXAValuations


class PEXAPropertyProfile(BaseModel):
    property: PEXAProperty


class PEXAGetByIdData(BaseModel):
    propertyProfile: PEXAPropertyProfile


class PEXAGetByIdResponse(BaseModel):
    data: PEXAGetByIdData


# --------------------------------------
# Minimal pill-level response structures
# --------------------------------------


class ValuationRange(BaseModel):
    min: int
    max: int


class PropertyValueAnswer(BaseModel):
    """Minimal structure for PROPERTY_VALUE pill.

    Derived from GraphQL fields:
    - value: propertyProfile.property.valuations.sale.value
    - range: propertyProfile.property.valuations.sale.range { min, max }
    """

    value: int
    range: ValuationRange
    confidence: Optional[str] = None
    property_id: Optional[str] = None
    full_address: Optional[str] = None


# --------------------------------------
# Question Classification Models
# --------------------------------------


class QuestionClassification(BaseModel):
    """Result of LLM question classification"""
    
    question_type: QuestionType = Field(
        description="The classified question type or 'unsupported' if no match"
    )
    confidence: float = Field(
        ge=0.0, le=1.0,
        description="Confidence score for the classification (0-1)"
    )
    reasoning: str = Field(
        description="Brief explanation of why this classification was chosen"
    )
    suggested_alternatives: Optional[List[str]] = Field(
        default=None,
        description="If unsupported, suggest similar questions we can answer"
    )


class UnsupportedQuestionResponse(BaseModel):
    """Response for questions we can't answer"""
    
    message: str = Field(
        description="Polite message explaining we can't help with this question"
    )
    suggested_questions: List[str] = Field(
        description="List of questions we can help with instead"
    )


class HistoricalGrowthAnswer(BaseModel):
    """Minimal structure for HISTORICAL_GROWTH pill.

    Derived from GraphQL fields:
    - delta_since_last_sale: valuations.sale.deltaSinceLastSale.value
    - last_sold_price: valuations.sale.lastSoldData.price
    - last_sold_year: valuations.sale.lastSoldData.year
    - current_value: valuations.sale.value
    """

    delta_since_last_sale: int
    current_value: int
    last_sold_price: Optional[int] = None
    last_sold_year: Optional[str] = None
    property_id: Optional[str] = None
    full_address: Optional[str] = None
