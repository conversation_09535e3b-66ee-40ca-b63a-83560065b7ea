import importlib
import os
from typing import Any, List, Optional

# NOTE: Import langfuse lazily via importlib to avoid linter "unresolved import" when package isn't installed yet
_m_langfuse = None
_m_decorators = None
_m_callback = None
_langfuse_client = None
_langfuse_context = None


def _env_true(v: Optional[str]) -> bool:
    return (v or "").lower() in ("1", "true", "yes", "on")


LANGFUSE_ENABLED = _env_true(os.getenv("ENABLE_LANGFUSE", "true"))


def _lazy_imports():
    global _m_langfuse, _m_decorators, _m_callback, _langfuse_context
    if _m_langfuse is not None:
        return
    try:
        import langfuse
        import langfuse.langchain  # Correct import path

        _m_langfuse = langfuse
        _m_decorators = langfuse  # observe is directly in langfuse module
        _m_callback = langfuse.langchain
        _langfuse_context = None  # Context not available in this version
    except Exception as e:
        _m_langfuse = None
        _m_decorators = None
        _m_callback = None
        _langfuse_context = None


def _init_client():
    _lazy_imports()
    if not LANGFUSE_ENABLED or _m_langfuse is None:
        return None
    public = os.getenv("LANGFUSE_PUBLIC_KEY")
    secret = os.getenv("LANGFUSE_SECRET_KEY")
    host = os.getenv("LANGFUSE_HOST", "https://cloud.langfuse.com")
    if not (public and secret):
        return None
    try:
        return _m_langfuse.Langfuse(public_key=public, secret_key=secret, host=host)
    except Exception:
        return None


_langfuse_client = _init_client()


def enabled() -> bool:
    return _langfuse_client is not None


def observe(*args, **kwargs):
    _lazy_imports()
    if enabled() and _m_decorators is not None and hasattr(_m_decorators, "observe"):
        return getattr(_m_decorators, "observe")(*args, **kwargs)

    def _noop(func):
        return func

    return _noop


def get_langchain_callbacks() -> List[Any]:
    _lazy_imports()
    if (
        enabled()
        and _m_callback is not None
        and hasattr(_m_callback, "CallbackHandler")
    ):
        return [getattr(_m_callback, "CallbackHandler")()]
    return []


def update_current_observation(**kwargs):
    # Note: langfuse_context not available in this version
    # The @observe decorator will capture function inputs/outputs automatically
    # This function is kept for API compatibility but does nothing
    pass


def mask_value(value: Optional[str]) -> Optional[str]:
    if not value:
        return value
    if len(value) <= 4:
        return value[0] + "***"
    return value[:2] + "***" + value[-2:]
