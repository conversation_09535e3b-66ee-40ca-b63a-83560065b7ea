import json
import os
from typing import Optional

import aiohttp

from backend.shared.models import (
    HistoricalGrowthAnswer,
    PEXAGetByIdResponse,
    PropertyValueAnswer,
    ValuationRange,
)
from backend.shared.tracing import observe, update_current_observation

GET_BY_ID_QUERY = """
query getById($value: String!) {
    propertyProfile(requestType: PROPERTYID, requestValue: $value) {
        property {
            id
            suburb
            postcode
            fullAddress
            valuations {
                sale {
                    range { min max }
                    value
                    confidence
                    deltaSinceLastSale { value }
                    lastSoldData { price year }
                }
            }
        }
    }
}
"""


class PEXAGraphQLClient:
    def __init__(self, endpoint: Optional[str] = None, token: Optional[str] = None):
        self.endpoint = endpoint or os.getenv("GRAPHQL_ENDPOINT")
        self.token = token or os.getenv("GRAPHQL_TOKEN")

    async def _post(self, query: str, variables: dict) -> dict:
        if not self.endpoint:
            raise RuntimeError("GRAPHQL_ENDPOINT is not configured")

        headers = {"Content-Type": "application/json"}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"

        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.endpoint,
                json={"query": query, "variables": variables},
                headers=headers,
            ) as resp:
                text = await resp.text()
                if resp.status >= 400:
                    raise RuntimeError(f"GraphQL HTTP {resp.status}: {text}")
                payload = json.loads(text)
                if "errors" in payload:
                    raise RuntimeError(f"GraphQL errors: {payload['errors']}")
                return payload

    @observe(name="Tool: PEXA.get_property_by_id")
    async def get_property_by_id(self, property_id: str) -> PEXAGetByIdResponse:
        update_current_observation(
            input={
                "endpoint": self.endpoint,
                "variables": {
                    "value": property_id[:2] + "***" if property_id else None
                },
            }
        )
        data = await self._post(GET_BY_ID_QUERY, {"value": property_id})
        return PEXAGetByIdResponse(**data)

    @staticmethod
    def to_property_value_answer(gql: PEXAGetByIdResponse) -> PropertyValueAnswer:
        prop = gql.data.propertyProfile.property
        sale = prop.valuations.sale
        return PropertyValueAnswer(
            value=sale.value,
            range=ValuationRange(min=sale.range.min, max=sale.range.max),
            confidence=sale.confidence,
            property_id=prop.id,
            full_address=prop.fullAddress,
        )

    @staticmethod
    def to_historical_growth_answer(gql: PEXAGetByIdResponse) -> HistoricalGrowthAnswer:
        prop = gql.data.propertyProfile.property
        sale = prop.valuations.sale

        delta = 0
        if sale.deltaSinceLastSale and sale.deltaSinceLastSale.value is not None:
            delta = sale.deltaSinceLastSale.value
        elif sale.lastSoldData and sale.lastSoldData.price is not None:
            delta = sale.value - sale.lastSoldData.price

        return HistoricalGrowthAnswer(
            delta_since_last_sale=delta,
            current_value=sale.value,
            last_sold_price=sale.lastSoldData.price if sale.lastSoldData else None,
            last_sold_year=sale.lastSoldData.year if sale.lastSoldData else None,
            property_id=prop.id,
            full_address=prop.fullAddress,
        )
