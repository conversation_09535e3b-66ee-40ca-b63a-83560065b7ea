"""
LangGraph workflow definitions for property analysis.
Demonstrates state-based agent workflow vs Pydantic AI's tool-based approach.
"""

import os
from typing import Annotated, Literal, TypedDict, Union

from langchain_core.messages import AIMessage, HumanMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, add_messages
from langgraph.prebuilt import ToolNode
from pydantic import SecretStr

from backend.shared.graphql_client import PEXAGraphQLClient
from backend.shared.models import (
    HistoricalGrowthAnswer,
    PropertyValueAnswer,
    QuestionClassification,
    QuestionType,
)
from backend.shared.tracing import (
    get_langchain_callbacks,
    observe,
    update_current_observation,
)


class PropertyAnalysisState(TypedDict):
    """State schema for the property analysis workflow"""

    messages: Annotated[list, add_messages]
    property_id: str
    question_type: QuestionType
    result: Union[PropertyValueAnswer, HistoricalGrowthAnswer, None]


def get_llm():
    """Get LLM instance with fallback handling"""
    api_key = os.getenv("OPENAI_API_KEY") or ""
    base_url = os.getenv("OPENAI_BASE_URL")
    if not api_key:
        return None
    return ChatOpenAI(
        model="gpt-4o-mini",
        api_key=SecretStr(api_key),
        base_url=base_url,
        temperature=0,
    )


def should_use_agent() -> bool:
    """Check if we have API key for LLM"""
    key = os.getenv("OPENAI_API_KEY") or ""
    return bool(key.strip())


async def classify_question_node(state: PropertyAnalysisState):
    """Node to classify user question using LLM"""
    if not should_use_agent():
        # Fallback classification logic
        return {"question_type": state["question_type"]}

    llm = get_llm()
    messages = [
        HumanMessage(
            content=f"""
        Classify this property question into one of these types:
        - PROPERTY_VALUE: Questions about current worth, valuation, market value
        - HISTORICAL_GROWTH: Questions about value changes, growth, performance over time
        - UNSUPPORTED: Other questions
        
        Question: {state["messages"][-1].content if state["messages"] else "property value"}
        
        Respond with just the type: PROPERTY_VALUE, HISTORICAL_GROWTH, or UNSUPPORTED
        """
        )
    ]

    assert llm is not None
    response = await llm.ainvoke(
        messages, config={"callbacks": get_langchain_callbacks()}
    )

    # Parse response to QuestionType
    content_raw = response.content
    content_str = content_raw if isinstance(content_raw, str) else str(content_raw)
    content = content_str.strip().upper()
    if "PROPERTY_VALUE" in content:
        question_type = QuestionType.PROPERTY_VALUE
    elif "HISTORICAL_GROWTH" in content:
        question_type = QuestionType.HISTORICAL_GROWTH
    else:
        question_type = QuestionType.UNSUPPORTED

    return {"question_type": question_type}


async def fetch_property_data_node(state: PropertyAnalysisState):
    """Node to fetch property data from GraphQL"""
    client = PEXAGraphQLClient()

    try:
        gql_response = await client.get_property_by_id(state["property_id"])

        if state["question_type"] == QuestionType.PROPERTY_VALUE:
            result = client.to_property_value_answer(gql_response)
        elif state["question_type"] == QuestionType.HISTORICAL_GROWTH:
            result = client.to_historical_growth_answer(gql_response)
        else:
            result = None

        return {"result": result}

    except Exception as e:
        print(f"Error fetching property data: {e}")
        return {"result": None}


async def format_response_node(state: PropertyAnalysisState):
    """Node to format the final response"""
    if not state["result"]:
        error_msg = AIMessage(content="Unable to fetch property data.")
        return {"messages": [error_msg]}

    # Format response based on question type and data
    if state["question_type"] == QuestionType.PROPERTY_VALUE and isinstance(
        state["result"], PropertyValueAnswer
    ):
        result = state["result"]
        formatted_text = (
            f"**Property Valuation**\n\n"
            f"🏠 Estimated Value: ${result.value:,}\n"
            f"📈 Range: ${result.range.min:,} - ${result.range.max:,}"
        )
        if result.confidence:
            formatted_text += f"\n🧭 Confidence: {result.confidence}"
        if result.full_address:
            formatted_text += f"\n📍 {result.full_address}"
    elif state["question_type"] == QuestionType.HISTORICAL_GROWTH and isinstance(
        state["result"], HistoricalGrowthAnswer
    ):
        result = state["result"]
        delta_emoji = "📈" if result.delta_since_last_sale >= 0 else "📉"
        formatted_text = (
            f"**Historical Growth (Since Last Sale)**\n\n"
            f"{delta_emoji} Delta Since Last Sold: ${result.delta_since_last_sale:,}\n"
            f"💵 Current Value: ${result.current_value:,}"
        )
        if result.last_sold_price:
            formatted_text += (
                f"\n💰 Last Sold: ${result.last_sold_price:,} ({result.last_sold_year})"
            )
        if result.full_address:
            formatted_text += f"\n📍 {result.full_address}"
    else:
        formatted_text = "Unable to format response for this question type."

    response_msg = AIMessage(content=formatted_text)
    return {"messages": [response_msg]}


def should_fetch_data(
    state: PropertyAnalysisState,
) -> Literal["fetch_data", "format_error"]:
    """Routing function to decide if we should fetch data"""
    if state["question_type"] == QuestionType.UNSUPPORTED:
        return "format_error"
    return "fetch_data"


def create_property_workflow():
    """Create the LangGraph workflow for property analysis"""

    # Create the workflow graph
    workflow = StateGraph(PropertyAnalysisState)

    # Add nodes
    workflow.add_node("classify", classify_question_node)
    workflow.add_node("fetch_data", fetch_property_data_node)
    workflow.add_node("format_response", format_response_node)
    workflow.add_node(
        "format_error",
        lambda state: {
            "messages": [
                AIMessage(
                    content="I can only help with property valuation and historical growth questions."
                )
            ]
        },
    )

    # Add edges
    workflow.set_entry_point("classify")
    workflow.add_conditional_edges(
        "classify",
        should_fetch_data,
        {"fetch_data": "fetch_data", "format_error": "format_error"},
    )
    workflow.add_edge("fetch_data", "format_response")
    workflow.add_edge("format_response", "__end__")
    workflow.add_edge("format_error", "__end__")

    return workflow.compile()


# Global workflow instance
property_workflow = create_property_workflow()


@observe(name="LG: run_property_analysis_workflow")
async def run_property_analysis_workflow(
    question_type: QuestionType, property_id: str, user_message: str = ""
) -> Union[PropertyValueAnswer, HistoricalGrowthAnswer]:
    """Run the LangGraph workflow for property analysis"""
    update_current_observation(
        input={
            "question_type": str(
                question_type.value
                if hasattr(question_type, "value")
                else question_type
            ),
            "property_id": property_id,
            "user_message": user_message,
        }
    )

    initial_state = PropertyAnalysisState(
        messages=[HumanMessage(content=user_message or "property analysis")],
        property_id=property_id,
        question_type=question_type,
        result=None,
    )

    # Execute the workflow
    final_state = await property_workflow.ainvoke(initial_state)

    if final_state["result"]:
        return final_state["result"]
    else:
        # Fallback: direct GraphQL call if workflow fails
        client = PEXAGraphQLClient()
        gql = await client.get_property_by_id(property_id)

        if question_type == QuestionType.PROPERTY_VALUE:
            return client.to_property_value_answer(gql)
        elif question_type == QuestionType.HISTORICAL_GROWTH:
            return client.to_historical_growth_answer(gql)
        else:
            raise ValueError(f"Unsupported question type: {question_type}")


@observe(name="LG: classify_user_question_workflow")
async def classify_user_question_workflow(user_question: str) -> QuestionClassification:
    """Use LangGraph workflow to classify user questions"""
    update_current_observation(input={"user_question": user_question})

    if not should_use_agent():
        # Fallback: simple keyword matching
        user_lower = user_question.lower()

        value_keywords = ["worth", "value", "price", "cost", "valuation", "market"]
        growth_keywords = [
            "growth",
            "grown",
            "increase",
            "change",
            "historical",
            "performance",
        ]

        value_score = sum(1 for keyword in value_keywords if keyword in user_lower)
        growth_score = sum(1 for keyword in growth_keywords if keyword in user_lower)

        if value_score > growth_score and value_score > 0:
            return QuestionClassification(
                question_type=QuestionType.PROPERTY_VALUE,
                confidence=min(0.8, value_score * 0.3),
                reasoning=f"Detected value-related keywords: {[k for k in value_keywords if k in user_lower]}",
            )
        elif growth_score > 0:
            return QuestionClassification(
                question_type=QuestionType.HISTORICAL_GROWTH,
                confidence=min(0.8, growth_score * 0.3),
                reasoning=f"Detected growth-related keywords: {[k for k in growth_keywords if k in user_lower]}",
            )
        else:
            return QuestionClassification(
                question_type=QuestionType.UNSUPPORTED,
                confidence=0.9,
                reasoning="No matching keywords found for supported question types",
                suggested_alternatives=[
                    "What's my property worth?",
                    "How much has my property grown in value?",
                ],
            )

    # Use LangGraph workflow for classification
    initial_state = PropertyAnalysisState(
        messages=[HumanMessage(content=user_question)],
        property_id="",
        question_type=QuestionType.UNSUPPORTED,
        result=None,
    )

    # Run just the classification node
    result_state = await classify_question_node(initial_state)

    return QuestionClassification(
        question_type=result_state["question_type"],
        confidence=0.85,  # Default confidence for LLM classification
        reasoning=f"LangGraph classified as {result_state['question_type']}",
    )


def get_workflow_visualization():
    """Get a text representation of the workflow structure"""
    return """
    LangGraph Property Analysis Workflow:
    
    [START] 
       ↓
    [classify_question_node] - Determines question type using LLM
       ↓
    {should_fetch_data} - Routes based on question type
       ↓                    ↓
    [fetch_data_node]    [format_error_node]
       ↓                    ↓
    [format_response_node]   [END]
       ↓
    [END]
    
    State Schema:
    - messages: Conversation history
    - property_id: Target property ID
    - question_type: Classified question type
    - result: Final property data result
    """
