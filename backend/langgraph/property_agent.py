"""
LangGraph property agent - alternative implementation to Pydantic AI version.
Demonstrates state-based workflow vs tool-based approach.
"""

from typing import Union

from backend.langgraph.workflows import (
    classify_user_question_workflow,
    run_property_analysis_workflow,
)
from backend.shared.models import (
    HistoricalGrowthAnswer,
    PropertyValueAnswer,
    QuestionClassification,
    QuestionType,
)
from backend.shared.tracing import observe, update_current_observation


@observe(name="LG: property_agent.run_property_analysis_agent")
async def run_property_analysis_agent(
    question_type: QuestionType, property_id: str
) -> Union[PropertyValueAnswer, HistoricalGrowthAnswer]:
    """
    LangGraph implementation of property analysis.
    Uses state-based workflow instead of tool-based agents.
    """
    update_current_observation(
        input={
            "question_type": str(
                question_type.value
                if hasattr(question_type, "value")
                else question_type
            ),
            "property_id": property_id,
        }
    )
    return await run_property_analysis_workflow(question_type, property_id)


@observe(name="LG: property_agent.classify_user_question")
async def classify_user_question(user_question: str) -> QuestionClassification:
    """LangGraph implementation of question classification"""
    update_current_observation(input={"user_question": user_question})
    return await classify_user_question_workflow(user_question)


def format_response_for_streaming(
    response_data: Union[PropertyValueAnswer, HistoricalGrowthAnswer],
    question_type: QuestionType,
) -> str:
    """Format response for streaming (shared logic with Pydantic AI version)"""
    if question_type == QuestionType.PROPERTY_VALUE and isinstance(
        response_data, PropertyValueAnswer
    ):
        return (
            f"**Property Valuation (via LangGraph)**\n\n"
            f"🏠 Estimated Value: ${response_data.value:,}\n"
            f"📈 Range: ${response_data.range.min:,} - ${response_data.range.max:,}"
            f"{f'\n🧭 Confidence: {response_data.confidence}' if response_data.confidence else ''}"
            f"{f'\n📍 {response_data.full_address}' if response_data.full_address else ''}"
            f"\n\n🔧 *Powered by LangGraph state workflows*"
        )

    if question_type == QuestionType.HISTORICAL_GROWTH and isinstance(
        response_data, HistoricalGrowthAnswer
    ):
        delta_emoji = "📈" if response_data.delta_since_last_sale >= 0 else "📉"
        return (
            f"**Historical Growth (via LangGraph)**\n\n"
            f"{delta_emoji} Delta Since Last Sold: ${response_data.delta_since_last_sale:,}\n"
            f"💵 Current Value: ${response_data.current_value:,}"
            f"{f'\n💰 Last Sold: ${response_data.last_sold_price:,} ({response_data.last_sold_year})' if response_data.last_sold_price else ''}"
            f"{f'\n📍 {response_data.full_address}' if response_data.full_address else ''}"
            f"\n\n🔧 *Powered by LangGraph state workflows*"
        )

    return "Unable to format response."


def format_unsupported_response(classification: QuestionClassification) -> str:
    """Format a helpful response for unsupported questions (LangGraph version)"""
    base_message = "I'm sorry, but I can only help with property valuation and historical growth questions. (LangGraph Agent)"

    if classification.suggested_alternatives:
        suggestions = "\n\n💡 **Try asking:**\n" + "\n".join(
            f"• {suggestion}" for suggestion in classification.suggested_alternatives
        )
    else:
        suggestions = "\n\n💡 **Try asking:**\n• What's my property worth?\n• How much has my property grown in value?"

    return base_message + suggestions + "\n\n🔧 *Powered by LangGraph state workflows*"
